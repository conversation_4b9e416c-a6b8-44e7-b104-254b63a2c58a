<button id="theme-toggle" class="theme-toggle" aria-label="切换主题">
	<svg class="sun-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
		<circle cx="12" cy="12" r="5"></circle>
		<line x1="12" y1="1" x2="12" y2="3"></line>
		<line x1="12" y1="21" x2="12" y2="23"></line>
		<line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
		<line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
		<line x1="1" y1="12" x2="3" y2="12"></line>
		<line x1="21" y1="12" x2="23" y2="12"></line>
		<line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
		<line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
	</svg>
	<svg class="moon-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
		<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
	</svg>
</button>

<script>
	// 检查本地存储中的主题设置
	const theme = localStorage.getItem('theme') || 
		(window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
	
	// 设置初始主题
	document.documentElement.setAttribute('data-theme', theme);
	
	// 主题切换按钮点击事件
	const themeToggle = document.getElementById('theme-toggle');
	if (themeToggle) {
		themeToggle.addEventListener('click', () => {
			const currentTheme = document.documentElement.getAttribute('data-theme');
			const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
			
			// 更新主题
			document.documentElement.setAttribute('data-theme', newTheme);
			localStorage.setItem('theme', newTheme);
			
			// 强制触发重绘
			document.body.style.display = 'none';
			document.body.offsetHeight; // 触发重排
			document.body.style.display = '';
		});
	}
</script>

<style>
	.theme-toggle {
		background: none;
		border: none;
		padding: 0.5rem;
		cursor: pointer;
		color: var(--icon-color);
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		transition: all 0.3s ease;
		position: relative;
		width: 40px;
		height: 40px;
	}

	.theme-toggle:hover {
		background-color: rgba(var(--gray), 0.1);
	}

	.sun-icon,
	.moon-icon {
		width: 24px;
		height: 24px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		transition: all 0.3s ease;
		pointer-events: none;
	}

	.moon-icon {
		opacity: 0;
		visibility: hidden;
	}

	[data-theme="dark"] .sun-icon {
		opacity: 0;
		visibility: hidden;
	}

	[data-theme="dark"] .moon-icon {
		opacity: 1;
		visibility: visible;
	}
</style> 