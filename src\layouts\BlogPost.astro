---
import type { CollectionEntry } from 'astro:content';
import BaseHead from '../components/BaseHead.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import FormattedDate from '../components/FormattedDate.astro';
import Comment from '../components/Comment.astro';
import BackToTop from '../components/Backtotop.astro';
import { Image } from 'astro:assets';
import heroA from '../assets/cooper-s-coding-notes.jpg';
import heroB from '../assets/cooper-s-seo-summary.jpg';
import heroC from '../assets/after-i-turn-off-alarm.webp';
import heroD from '../assets/about-cooper.jpg';
import heroE from '../assets/tools.gif';
import heroF from '../assets/how-to-use-vpn-correctly.jpg';
import heroG from '../assets/root-one-plus-8t-9008-oxgen-os.jpg';
import heroH from '../assets/what-is-mcp.png';


type Props = CollectionEntry<'blog'>['data'];

const { title, description, pubDate, updatedDate, heroImage } = Astro.props;
const heroImageMap = {
  'hero-a': heroA,
  'hero-b': heroB,
  'hero-c': heroC,
  'hero-d': heroD,
  'hero-e': heroE,
  'hero-f': heroF,
  "hero-g": heroG,
  "hero-h": heroH,
};

// 确保当前的 hero 图片与 frontmatter 中的 heroImage 对应
const currentHero = heroImage?.toLowerCase(); // 动态获取 heroImage 并小写化
---

<html lang="en">
	<head>		
		<BaseHead title={title} description={description} />
		<style>
			main {
				width: calc(100% - 2em);
				max-width: 100%;
				margin: 0;
			}
			.hero-image {
				width: 100%;
			}
			.hero-image img {
				display: block;
				margin: 0 auto;
				border-radius: 12px;
				box-shadow: var(--box-shadow);
			}
			.prose {
				width: 720px;
				max-width: calc(100% - 2em);
				margin: auto;
				padding: 1em;
				color: var(--text-color);
			}
			.title {
				margin-bottom: 1em;
				padding: 1em 0;
				text-align: center;
				line-height: 1;
			}
			.title h1 {
				margin: 0 0 0.5em 0;
				color: var(--text-color);
			}
			.date {
				margin-bottom: 0.5em;
				color: var(--text-color-secondary);
			}
			.last-updated-on {
				font-style: italic;
				color: var(--text-color-secondary);
			}
		</style>

		

		<script type="text/javascript">
			(function(c,l,a,r,i,t,y){
				c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
				t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
				y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
			})(window, document, "clarity", "script", "r4ew4dl716");
		</script>
        <!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-DKECP77S2Y"></script>
		<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());

		gtag('config', 'G-DKECP77S2Y');
		</script>

	</head>
	<body>
		<Header />
		<main>
			<article>
				<div class="hero-image">
					<Image
					src={heroImageMap[currentHero as keyof typeof heroImageMap]}
					alt="title"
					width={1020}
					height={510}
					loading="eager"
				  />				  
				</div>
				<div class="prose">
					<div class="title">
						<div class="date">
							<FormattedDate date={pubDate} />
							{
								updatedDate && (
									<div class="last-updated-on">
										Last updated on <FormattedDate date={updatedDate} />
									</div>
								)
							}
						</div>
						<h1>{title}</h1>
						<hr />
					</div>
					<slot />
				</div>
			</article>
			<BackToTop />
			<Comment />
		</main>
		<Footer />
	</body>
</html>
