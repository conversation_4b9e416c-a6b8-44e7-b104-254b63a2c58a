/*
  The CSS in this style tag is based off of Bear Blog's default CSS.
  https://github.com/HermanMartinus/bearblog/blob/297026a877bc2ab2b3bdfbd6b9f7961c350917dd/templates/styles/blog/default.css
  License MIT: https://github.com/<PERSON>M<PERSON>inus/bearblog/blob/master/LICENSE.md
 */

:root {
	color-scheme: light;
	--accent: #2337ff;
	--accent-dark: #000d8a;
	--black: 15, 18, 25;
	--gray: 96, 115, 159;
	--gray-light: 229, 233, 240;
	--gray-dark: 34, 41, 57;
	--gray-gradient: rgba(var(--gray-light), 50%), #fff;
	--box-shadow:
		0 2px 6px rgba(var(--gray), 25%), 0 8px 24px rgba(var(--gray), 33%),
		0 16px 32px rgba(var(--gray), 33%);
	--bg-color: #fff;
	--text-color: rgb(var(--gray-dark));
	--header-bg: #fff;
	--header-shadow: 0 2px 8px rgba(var(--black), 5%);
	--code-bg: rgb(var(--gray-light));
	--blockquote-border: var(--accent);
	--hr-color: rgb(var(--gray-light));
	--link-color: var(--accent);
	--toc-bg: #f9fafb;
	--toc-border: #e5e7eb;
	--toc-link: #2563eb;
	--toc-hover: #eff6ff;
	--icon-color: rgb(var(--gray-dark));
	--bg-accent: rgba(var(--gray), 0.1);
}

[data-theme="dark"] {
	color-scheme: dark;
	--bg-color: rgb(var(--black));
	--text-color: rgb(var(--gray-light));
	--header-bg: rgb(var(--black));
	--header-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
	--code-bg: rgb(var(--gray-dark));
	--blockquote-border: var(--accent);
	--hr-color: rgb(var(--gray-dark));
	--link-color: var(--accent);
	--toc-bg: rgb(var(--gray-dark));
	--toc-border: rgb(var(--gray));
	--toc-link: var(--accent);
	--toc-hover: rgba(var(--gray), 0.2);
	--icon-color: rgb(var(--gray-light));
	--bg-accent: rgba(var(--gray-light), 0.1);
}

@font-face {
	font-family: 'MapleMono';
	src: url('/fonts/MapleMono-Regular.woff2') format('woff2');
	font-weight: 400;
	font-style: normal;
	font-display: swap;
}
@font-face {
	font-family: 'MapleMono';
	src: url('/fonts/MapleMono-Bold.woff2') format('woff2');
	font-weight: 700;
	font-style: normal;
	font-display: swap;
}
body {
	font-family: 'MapleMono', sans-serif;
	margin: 0;
	padding: 0;
	text-align: left;
	background: var(--bg-color);
	word-wrap: break-word;
	overflow-wrap: break-word;
	color: var(--text-color);
	font-size: 20px;
	line-height: 1.7;
	transition: background-color 0.3s ease, color 0.3s ease;
}
main {
	width: 720px;
	max-width: calc(100% - 2em);
	margin: auto;
	padding: 3em 1em;
	color: var(--text-color);
}
h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0 0 0.5rem 0;
	color: var(--text-color);
	line-height: 1.2;
}
h1 {
	font-size: 3.052em;
}
h2 {
	font-size: 2.441em;
}
h3 {
	font-size: 1.953em;
}
h4 {
	font-size: 1.563em;
}
h5 {
	font-size: 1.25em;
}
strong,
b {
	font-weight: 700;
}
a {
	color: var(--link-color);
	transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
a:hover {
	color: var(--accent);
}
p {
	margin-bottom: 1em;
	color: var(--text-color);
}
.prose p {
	margin-bottom: 2em;
	color: var(--text-color);
}
textarea {
	width: 100%;
	font-size: 16px;
}
input {
	font-size: 16px;
}
table {
	width: 100%;
}
img {
	max-width: 100%;
	height: auto;
	border-radius: 8px;
}
code {
	padding: 2px 5px;
	background-color: var(--code-bg);
	border-radius: 2px;
}
pre {
	padding: 1.5em;
	border-radius: 8px;
}
pre > code {
	all: unset;
}
blockquote {
	border-left: 4px solid var(--blockquote-border);
	padding: 0 0 0 20px;
	margin: 0px;
	font-size: 1.333em;
	color: var(--text-color);
}
hr {
	border: none;
	border-top: 1px solid var(--hr-color);
}
@media (max-width: 720px) {
	body {
		font-size: 18px;
	}
	main {
		padding: 1em;
	}
}

.sr-only {
	border: 0;
	padding: 0;
	margin: 0;
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	/* IE6, IE7 - a 0 height clip, off to the bottom right of the visible 1px box */
	clip: rect(1px 1px 1px 1px);
	/* maybe deprecated but we need to support legacy browsers */
	clip: rect(1px, 1px, 1px, 1px);
	/* modern browsers, clip-path works inwards from each corner */
	clip-path: inset(50%);
	/* added line to stop words getting smushed together (as they go onto separate lines and some screen readers do not understand line feeds as a space */
	white-space: nowrap;
}
.toc-box {
    background-color: var(--toc-bg);
    border: 1px solid var(--toc-border);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  }

  .toc-box ul {
    list-style: none;
    padding: 0;
  }

  .toc-box a {
    display: block;
    padding: 6px 8px;
    text-decoration: none;
    color: var(--toc-link);
    border-radius: 0.25rem;
    transition: background 0.2s ease;
  }

  .toc-box a:hover {
    background-color: var(--toc-hover);
  }

  
