---
// src/components/Comment.astro
const {
  repo = "你的用户名/你的仓库名",
  repoId = "你的-repo-id",
  category = "你的Discussions分类",
  categoryId = "你的-category-id",
  mapping = "pathname",
  theme = "light",
  lang = "en"
} = Astro.props;
---

<div id="giscus-comments" class="mt-8"></div>

<script src="https://giscus.app/client.js"
        data-repo="skywalker23241/J.Cooper-s-Log"
        data-repo-id="R_kgDOOZeiLQ"
        data-category="General"
        data-category-id="DIC_kwDOOZeiLc4CpHLs"
        data-mapping="pathname"
        data-strict="0"
        data-reactions-enabled="1"
        data-emit-metadata="1"
        data-input-position="top"
        data-theme="preferred_color_scheme"
        data-lang="zh-CN"
        data-loading="lazy"
        crossorigin="anonymous"
        async>
</script>
