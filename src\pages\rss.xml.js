import { getCollection } from 'astro:content';
import { SITE_TITLE, SITE_DESCRIPTION } from '../consts';

export async function GET({ site }) {
	const posts = await getCollection('blog');

	const itemsXml = posts.map((post) => `
		<item>
			<title>${post.data.title}</title>
			<link>${site}/blog/${post.id}/</link>
			<pubDate>${new Date(post.data.pubDate).toUTCString()}</pubDate>
			<description><![CDATA[${post.data.description}]]></description>
		</item>
	`).join('');

	const rss = `<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="data:text/xsl;base64,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"?>
<rss version="2.0">
  <channel>
    <title>${SITE_TITLE}</title>
    <link>${site}</link>
    <description>${SITE_DESCRIPTION}</description>
    <language>en-us</language>
    ${itemsXml}
  </channel>
</rss>`;

	return new Response(rss, {
		headers: {
			'Content-Type': 'application/xml',
		},
	});
}
