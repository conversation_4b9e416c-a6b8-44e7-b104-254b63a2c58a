---
import HeaderLink from './HeaderLink.astro';
import ThemeToggle from './ThemeToggle.astro';
import { SITE_TITLE } from '../consts';
---

<header>
	<div class="container">
		<nav class="nav-main">
			<!-- Logo/Brand -->
			<div class="brand">
				<h1><a href="/" class="brand-link">{SITE_TITLE}</a></h1>
			</div>

			<!-- Main Navigation -->
			<div class="nav-links">
				<HeaderLink href="/" title="Howdy">😎</HeaderLink>
				<HeaderLink href="/blog" title="<PERSON>'s Log">📝</HeaderLink>
				<HeaderLink href="/about" title="About Cooper">👤</HeaderLink>
				<HeaderLink href="/tools" title="<PERSON>'s Tools">🧰</HeaderLink>
			</div>

			<!-- Right Side Actions -->
			<div class="nav-actions">
				<div class="social-links">
					<a href="https://x.com/Leonozzzzz" target="_blank" title="Follow <PERSON><PERSON>Cooper on X" class="social-link">
						<span class="sr-only">Follow J.<PERSON> on X</span>
						<svg viewBox="0 0 16 16" aria-hidden="true" width="20" height="20">
							<path fill="currentColor" d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"></path>
						</svg>
					</a>
					<a href="https://github.com/skywalker23241" target="_blank" title="Go to J.Cooper's GitHub" class="social-link">
						<span class="sr-only">Go to J.Cooper's GitHub</span>
						<svg viewBox="0 0 16 16" aria-hidden="true" width="20" height="20">
							<path fill="currentColor" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"></path>
						</svg>
					</a>
					<a href="/rss.xml" target="_blank" title="Subscribe to RSS Feed" class="social-link">
						<span class="sr-only">Subscribe to RSS Feed</span>
						<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
							<path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"/>
							<path d="M5.5 12a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m-3-8.5a1 1 0 0 1 1-1c5.523 0 10 4.477 10 10a1 1 0 1 1-2 0 8 8 0 0 0-8-8 1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1 6 6 0 0 1 6 6 1 1 0 1 1-2 0 4 4 0 0 0-4-4 1 1 0 0 1-1-1"/>
						</svg>
					</a>
				</div>
				<ThemeToggle />
			</div>

			<!-- Mobile Menu Button -->
			<button class="mobile-menu-toggle" aria-label="Toggle mobile menu" type="button">
				<span class="hamburger"></span>
				<span class="hamburger"></span>
				<span class="hamburger"></span>
			</button>
		</nav>

		<!-- Mobile Navigation -->
		<nav class="nav-mobile">
			<div class="mobile-nav-links">
				<HeaderLink href="/" title="Howdy">😎 Howdy</HeaderLink>
				<HeaderLink href="/blog" title="Cooper's Log">📝 Cooper's Log</HeaderLink>
				<HeaderLink href="/about" title="About Cooper">👤 About Cooper</HeaderLink>
				<HeaderLink href="/tools" title="Cooper's Tools">🧰 Cooper's Tools</HeaderLink>
			</div>
		</nav>
	</div>
</header>
<style>
	/* Header Container */
	header {
		position: sticky;
		top: 0;
		z-index: 100;
		background: var(--header-bg);
		box-shadow: var(--header-shadow);
		border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
	}

	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 1rem;
	}

	/* Main Navigation */
	.nav-main {
		display: grid;
		grid-template-columns: 1fr auto 1fr;
		align-items: center;
		min-height: 4rem;
		gap: 1rem;
	}

	/* Brand/Logo */
	.brand {
		grid-column: 1;
		justify-self: start;
	}

	.brand h1 {
		margin: 0;
		font-size: 1.25rem;
		font-weight: 600;
	}

	.brand-link {
		color: var(--text-color, var(--black));
		text-decoration: none;
		transition: color 0.2s ease;
	}

	.brand-link:hover {
		color: var(--accent);
	}

	/* Navigation Links */
	.nav-links {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		grid-column: 2;
		position: relative;
	}

	/* Navigation Actions */
	.nav-actions {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		gap: 1rem;
		grid-column: 3;
	}

	/* Social Links */
	.social-links {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.social-link {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0.5rem;
		color: var(--text-color, var(--black));
		text-decoration: none;
		border-radius: 0.375rem;
		transition: all 0.2s ease;
	}

	.social-link:hover {
		color: var(--accent);
		background-color: var(--bg-accent, rgba(0, 0, 0, 0.05));
	}

	/* Mobile Menu */
	.mobile-menu-toggle {
		display: none;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 2.5rem;
		height: 2.5rem;
		background: none;
		border: none;
		cursor: pointer;
		padding: 0.5rem;
		border-radius: 0.375rem;
		transition: background-color 0.2s ease;
	}

	.mobile-menu-toggle:hover {
		background-color: var(--bg-accent, rgba(0, 0, 0, 0.05));
	}

	.hamburger {
		width: 1.25rem;
		height: 2px;
		background-color: var(--text-color, var(--black));
		margin: 2px 0;
		transition: 0.3s;
		border-radius: 1px;
	}

	/* Mobile Navigation */
	.nav-mobile {
		display: none;
		border-top: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
		padding: 1rem 0;
	}

	.mobile-nav-links {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.nav-main {
			display: flex;
			justify-content: space-between;
		}

		.nav-links {
			display: none;
		}

		.social-links {
			display: none;
		}

		.mobile-menu-toggle {
			display: flex;
		}

		.nav-mobile.active {
			display: block;
		}
	}

	@media (max-width: 480px) {
		.container {
			padding: 0 0.75rem;
		}

		.nav-main {
			min-height: 3.5rem;
		}

		.brand h1 {
			font-size: 1.125rem;
		}
	}

	/* Accessibility */
	.sr-only {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
	}

	/* Navigation link styles with higher specificity */
	.nav-links a,
	.mobile-nav-links a {
		padding: 1em 0.5em;
		color: var(--text-color) !important;
		border-bottom: 4px solid transparent;
		text-decoration: none;
		transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
					border-bottom-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
					background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		border-radius: 0.375rem;
	}

	.nav-links a.active,
	.mobile-nav-links a.active {
		border-bottom-color: var(--accent);
		color: var(--accent) !important;
	}

	.nav-links a:hover,
	.mobile-nav-links a:hover {
		border-bottom-color: var(--accent);
		color: var(--accent) !important;
		background-color: var(--bg-accent, rgba(0, 0, 0, 0.05));
	}

	/* Brand link styles */
	.brand-link {
		transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
	}
</style>

<script>
	// Mobile menu toggle functionality
	document.addEventListener('DOMContentLoaded', () => {
		const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
		const mobileNav = document.querySelector('.nav-mobile');

		if (mobileMenuToggle && mobileNav) {
			mobileMenuToggle.addEventListener('click', () => {
				mobileNav.classList.toggle('active');
				mobileMenuToggle.setAttribute('aria-expanded',
					mobileNav.classList.contains('active') ? 'true' : 'false'
				);
			});

			// Close mobile menu when clicking outside
			document.addEventListener('click', (e) => {
				const target = e.target as Node;
				if (target && !mobileMenuToggle.contains(target) && !mobileNav.contains(target)) {
					mobileNav.classList.remove('active');
					mobileMenuToggle.setAttribute('aria-expanded', 'false');
				}
			});

			// Close mobile menu on window resize if it gets too wide
			window.addEventListener('resize', () => {
				if (window.innerWidth > 768) {
					mobileNav.classList.remove('active');
					mobileMenuToggle.setAttribute('aria-expanded', 'false');
				}
			});
		}
	});
</script>
